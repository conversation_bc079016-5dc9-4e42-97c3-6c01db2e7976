#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
断层分析报告生成模块
提供个股和板块资金流断层分析报告生成功能
"""

from utils.formatters import format_amount


def generate_stock_flow_report(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None):
    """个股资金流智能报告生成器"""
    
    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]
        
        return (f"【--- 个股资金流{competition_type}，无明显龙头 ---】\n"
                f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")

    # 基础信息
    gap_position = max_gap["position"]
    gap_ratio = max_gap["ratio"]
    gap_amount = max_gap["amount"]
    
    # 领先集团信息
    leader_names = group_info["members"]
    leader_amounts = group_info["amounts"]
    
    # 构建报告
    if total_score >= 85:
        # 超强断层
        leader_display = " | ".join([f"{name}({format_amount(amt)})" for name, amt in zip(leader_names, leader_amounts)])
        return (f"🔥【超强断层】个股资金流出现{gap_ratio:.1f}倍断层！\n"
                f"  断层位置: 第{gap_position}名后 (断层金额: {format_amount(gap_amount)})\n"
                f"  领先集团: 【{leader_display}】\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                f"  综合评分: {total_score:.1f}/100 ⭐⭐⭐")
    
    elif total_score >= 70:
        # 强断层
        leader_display = " | ".join([f"{name}({format_amount(amt)})" for name, amt in zip(leader_names, leader_amounts)])
        return (f"⚡【强断层】个股资金流出现{gap_ratio:.1f}倍断层\n"
                f"  断层位置: 第{gap_position}名后 (断层金额: {format_amount(gap_amount)})\n"
                f"  领先集团: 【{leader_display}】\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}\n"
                f"  综合评分: {total_score:.1f}/100 ⭐⭐")
    
    elif total_score >= 55:
        # 中等断层
        leader_display = " | ".join([f"{name}" for name in leader_names])
        return (f"📊【中等断层】个股资金流出现{gap_ratio:.1f}倍断层\n"
                f"  断层位置: 第{gap_position}名后\n"
                f"  领先集团: 【{leader_display}】\n"
                f"  综合评分: {total_score:.1f}/100 ⭐")
    
    else:
        return f"📊 动态阈值触发！虽然刚刚突破动态门槛，但在当前市场环境下已属异常，建议观察后续发展。"


def generate_stock_flow_report_with_ignition(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info=None, ignition_signals=None):
    """个股资金流智能报告生成器（包含点火信号）"""

    # 1. 首先显示点火信号（如果有）
    ignition_report = ""
    if ignition_signals:
        ignition_report = generate_ignition_report(ignition_signals) + "\n\n"

    # 2. 然后显示断层分析报告
    gap_report = generate_stock_flow_report(market_state, max_gap, group_info, thresholds, total_score, scores, competition_info)
    
    return ignition_report + gap_report


def generate_ignition_report(ignition_signals):
    """生成点火信号报告"""
    if not ignition_signals:
        return ""
    
    report_lines = ["🔥【点火信号检测】"]
    
    for signal in ignition_signals:
        signal_type = signal.get('signal_type', '未知信号')
        stock_name = signal.get('stock_name', '未知股票')
        current_amount = signal.get('current_amount', 0)
        signal_strength = signal.get('signal_strength', '中等')
        
        report_lines.append(f"  {signal_type}: {stock_name} ({format_amount(current_amount)}) - {signal_strength}")
    
    return "\n".join(report_lines)


def generate_smart_report(sector_type, market_state, max_gap, group_info, thresholds, total_score, scores,
                          competition_info=None, current_time=None, data_dir=None, mover_stocks=None, 
                          stock_flow_data=None, ignition_detector=None, df_sectors=None):
    """V9.0 智能报告生成器：生成详细的分析报告（龙头评分增强版）"""

    # 优先检查竞争激烈情况
    if competition_info and competition_info["is_competitive"]:
        competition_type = competition_info["competition_type"]
        leading_group = competition_info["leading_group"]
        max_ratio = competition_info["max_ratio"]

        return (f"【--- {sector_type}板块{competition_type}，无明显龙头 ---】\n"
                f"  竞争格局: 【{', '.join(leading_group)}】势均力敌\n"
                f"  最大差距: {max_ratio:.2f}倍 (各方实力接近)\n"
                f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")

    # 基础信息提取
    gap_position = max_gap["position"]
    gap_ratio = max_gap["ratio"]
    gap_amount = max_gap["amount"]
    
    # 领先集团信息
    leader_names = group_info["members"]
    leader_amounts = group_info["amounts"]
    
    # 构建报告行
    report_lines = []
    
    if total_score >= 85:
        # 超强断层
        leader_display = " | ".join([f"{name}({format_amount(amt)})" for name, amt in zip(leader_names, leader_amounts)])
        report_lines.extend([
            f"🔥【超强断层】{sector_type}板块出现{gap_ratio:.1f}倍断层！",
            f"  断层位置: 第{gap_position}名后 (断层金额: {format_amount(gap_amount)})",
            f"  领先集团: 【{leader_display}】",
            f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}",
            f"  综合评分: {total_score:.1f}/100 ⭐⭐⭐"
        ])
    
    elif total_score >= 70:
        # 强断层
        leader_display = " | ".join([f"{name}({format_amount(amt)})" for name, amt in zip(leader_names, leader_amounts)])
        report_lines.extend([
            f"⚡【强断层】{sector_type}板块出现{gap_ratio:.1f}倍断层",
            f"  断层位置: 第{gap_position}名后 (断层金额: {format_amount(gap_amount)})",
            f"  领先集团: 【{leader_display}】",
            f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}",
            f"  综合评分: {total_score:.1f}/100 ⭐⭐"
        ])
    
    elif total_score >= 55:
        # 中等断层
        leader_display = " | ".join([f"{name}" for name in leader_names])
        report_lines.extend([
            f"📊【中等断层】{sector_type}板块出现{gap_ratio:.1f}倍断层",
            f"  断层位置: 第{gap_position}名后",
            f"  领先集团: 【{leader_display}】",
            f"  综合评分: {total_score:.1f}/100 ⭐"
        ])
    
    else:
        report_lines.append(f"📊 {sector_type}板块动态阈值触发！虽然刚刚突破动态门槛，但在当前市场环境下已属异常，建议观察后续发展。")

    base_report = "\n".join(report_lines)

    # 【新增】断层龙头内部个股资金流分析
    if current_time is not None and data_dir is not None:
        internal_reports = []

        # 为每个领先集团成员生成内部分析
        for i, leader_name in enumerate(group_info['members']):
            # 获取对应的板块总资金量
            sector_total_amount = group_info["amounts"][i] if i < len(group_info["amounts"]) else group_info["amounts"][0]
            # 【【【修改：传入 mover_stocks, stock_flow_data, 和 ignition_detector】】】
            internal_report = analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type,
                                                           sector_total_amount, mover_stocks, stock_flow_data, ignition_detector)
            internal_reports.append(internal_report)

        # 合并所有内部分析报告
        all_internal_reports = "\n".join(internal_reports)
        return base_report + "\n" + all_internal_reports
    else:
        return base_report


def analyze_sector_internal_flow(leader_name, current_time, data_dir, sector_type, sector_total_amount, mover_stocks, stock_flow_data, ignition_detector):
    """分析板块内部个股资金流（占位函数，需要从主文件中提取完整实现）"""
    # 这是一个占位函数，实际实现需要从主文件中提取
    return f"  【{leader_name}】内部分析: 待实现"


def generate_marshal_summary_report(market_snapshot, concepts_df, industries_df, stock_flow_data, limit_up_stocks, current_sim_time):
    """
    【战情总结】模拟顶级游资视角的盘面解读报告

    通过联动校验不同模块的数据，自动识别"绝对主线"、"奇袭支线"、"最强补涨"三大核心盘面特征

    参数:
    - market_snapshot (dict): 包含当前时间点所有关键分析结果的字典
    - concepts_df (DataFrame): 排序后的概念板块数据
    - industries_df (DataFrame): 排序后的行业板块数据
    - stock_flow_data (DataFrame): 排序后的个股资金流数据
    - limit_up_stocks (list of dicts): 当前的涨停股池数据
    - current_sim_time (datetime.time): 当前模拟时间点

    返回值: 无，函数直接通过 print 输出战情总结报告
    """

    print("\n" + "⚔️" * 50)
    print("⚔️ 【战情总结】顶级游资视角的盘面解读 ⚔️")
    print("⚔️" * 50)

    # 第一步：识别"绝对主线"（主战场确认）
    _identify_absolute_mainline(market_snapshot, concepts_df, industries_df, stock_flow_data)

    # 第二步：识别"奇袭支线"（机会挖掘）
    _identify_surprise_sidelines(market_snapshot, concepts_df, industries_df, limit_up_stocks)

    # 第三步：识别"最强补涨"（跟风机会）
    _identify_strongest_catch_up(market_snapshot, stock_flow_data, limit_up_stocks, current_sim_time)

    print("⚔️" * 50)


def _identify_absolute_mainline(market_snapshot, concepts_df, industries_df, stock_flow_data):
    """识别绝对主线（占位函数）"""
    print("🎯 【绝对主线识别】: 待实现")


def _identify_surprise_sidelines(market_snapshot, concepts_df, industries_df, limit_up_stocks):
    """识别奇袭支线（占位函数）"""
    print("⚡ 【奇袭支线识别】: 待实现")


def _identify_strongest_catch_up(market_snapshot, stock_flow_data, limit_up_stocks, current_sim_time):
    """识别最强补涨（占位函数）"""
    print("📈 【最强补涨识别】: 待实现")
