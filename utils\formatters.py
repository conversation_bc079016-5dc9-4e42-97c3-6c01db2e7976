#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化工具模块
提供数据格式化和转换功能
"""


def format_amount(amount):
    """格式化金额显示"""
    if amount is None or not isinstance(amount, (int, float)): 
        return 'N/A'
    if abs(amount) >= 1e8: 
        return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: 
        return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def convert_to_float(value):
    """将各种格式的数值转换为浮点数"""
    if isinstance(value, str):
        try:
            # 处理中文单位
            value = value.replace('%', '').replace(',', '')
            if '亿' in value:
                return float(value.replace('亿', '')) * 1e8
            elif '万' in value:
                return float(value.replace('万', '')) * 1e4
            else:
                return float(value)
        except (ValueError, TypeError):
            return 0.0
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0
