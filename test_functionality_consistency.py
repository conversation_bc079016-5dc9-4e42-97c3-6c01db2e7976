#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能一致性测试
验证模块化后的功能与原始功能完全一致
"""

import pandas as pd
import numpy as np

def test_data_parsing_consistency():
    """测试数据解析功能的一致性"""
    print("=== 测试数据解析功能一致性 ===")
    
    try:
        from utils.data_parser import (
            parse_standard_format, parse_ths_format, parse_exchange_format,
            apply_column_mapping, get_stock_flow_file_format, apply_stock_filter
        )
        from utils.formatters import convert_to_float
        
        # 创建测试数据集
        test_data_sets = [
            # 标准格式数据
            {
                'name': '标准格式',
                'data': pd.DataFrame({
                    '名称': ['股票A', '股票B', '股票C'],
                    '代码': ['000001', '000002', '000003'],
                    '今日主力净流入-净额': ['1000万', '500万', '-200万'],
                    '今日涨跌幅': [5.2, 2.1, -1.5],
                    '最新价': [10.5, 8.2, 12.8]
                }),
                'parser': parse_standard_format
            },
            # 同花顺格式数据（有现成净流入）
            {
                'name': '同花顺格式(现成净流入)',
                'data': pd.DataFrame({
                    '名称': ['股票A', '股票B'],
                    '今日主力净流入-净额': ['2000万', '800万'],
                    '涨跌幅': [3.5, 1.2]
                }),
                'parser': parse_ths_format
            },
            # 同花顺格式数据（需要计算净流入）
            {
                'name': '同花顺格式(计算净流入)',
                'data': pd.DataFrame({
                    '名称': ['股票A', '股票B'],
                    '流入资金': ['3000万', '1500万'],
                    '流出资金': ['1000万', '700万'],
                    '涨跌幅': [4.2, 2.8]
                }),
                'parser': parse_ths_format
            },
            # 交易所格式数据
            {
                'name': '交易所格式',
                'data': pd.DataFrame({
                    '名称': ['股票A', '股票B'],
                    '代码': ['000001', '000002'],
                    '今日主力净流入-净额': ['1500万', '600万'],
                    '今日超大单净流入-净额': ['800万', '300万'],
                    '最新价': [15.2, 9.8]
                }),
                'parser': parse_exchange_format
            }
        ]
        
        all_passed = True
        
        for test_set in test_data_sets:
            print(f"\n测试 {test_set['name']}:")
            
            # 解析数据
            result = test_set['parser'](test_set['data'].copy())
            
            if result is None:
                print(f"  ✗ {test_set['name']} 解析失败")
                all_passed = False
                continue
            
            # 验证基本要求
            checks = [
                ('包含名称列', '名称' in result.columns),
                ('数据行数正确', len(result) == len(test_set['data'])),
                ('包含净流入列', '今日主力净流入-净额' in result.columns),
                ('净流入数据为数值', result['今日主力净流入-净额'].dtype in [np.float64, np.int64])
            ]
            
            for check_name, check_result in checks:
                status = "✓" if check_result else "✗"
                print(f"  {status} {check_name}")
                if not check_result:
                    all_passed = False
            
            # 验证数据转换正确性
            if '今日主力净流入-净额' in result.columns:
                # 检查数值转换是否正确
                first_value = result['今日主力净流入-净额'].iloc[0]
                if isinstance(first_value, (int, float)) and first_value > 0:
                    print(f"  ✓ 数值转换正确: {first_value}")
                else:
                    print(f"  ✗ 数值转换异常: {first_value}")
                    all_passed = False
        
        if all_passed:
            print("\n✅ 所有数据解析功能一致性测试通过")
        else:
            print("\n❌ 部分数据解析功能一致性测试失败")
        
        return all_passed
        
    except Exception as e:
        print(f"✗ 数据解析功能一致性测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_formatting_consistency():
    """测试格式化功能的一致性"""
    print("\n=== 测试格式化功能一致性 ===")
    
    try:
        from utils.formatters import format_amount, convert_to_float
        
        # 测试数据转换的一致性
        test_cases = [
            # (输入, 期望的convert_to_float结果, 期望的format_amount结果)
            ('1000万', 10000000.0, '1000.00万'),
            ('2.5亿', 250000000.0, '2.50亿'),
            ('500', 500.0, '500.00'),
            ('1.23万', 12300.0, '1.23万'),
            (None, 0.0, 'N/A'),
            ('', 0.0, 'N/A'),
            ('invalid', 0.0, 'N/A')
        ]
        
        all_passed = True
        
        print("测试数值转换和格式化一致性:")
        for input_val, expected_float, expected_format in test_cases:
            # 测试转换
            float_result = convert_to_float(input_val)
            float_ok = abs(float_result - expected_float) < 0.01
            
            # 测试格式化
            if float_result > 0:
                format_result = format_amount(float_result)
                format_ok = format_result == expected_format
            else:
                format_result = format_amount(input_val)
                format_ok = format_result == expected_format
            
            status = "✓" if (float_ok and format_ok) else "✗"
            print(f"  {status} {input_val} -> {float_result} -> {format_result}")
            
            if not (float_ok and format_ok):
                all_passed = False
                print(f"    期望: {expected_float} -> {expected_format}")
        
        # 测试往返转换的一致性
        print("\n测试往返转换一致性:")
        test_values = [1000000, 50000000, 1500000000, 123.45]
        
        for value in test_values:
            formatted = format_amount(value)
            converted_back = convert_to_float(formatted)
            
            # 允许小的精度误差
            is_consistent = abs(value - converted_back) < 0.01
            status = "✓" if is_consistent else "✗"
            print(f"  {status} {value} -> {formatted} -> {converted_back}")
            
            if not is_consistent:
                all_passed = False
        
        if all_passed:
            print("\n✅ 所有格式化功能一致性测试通过")
        else:
            print("\n❌ 部分格式化功能一致性测试失败")
        
        return all_passed
        
    except Exception as e:
        print(f"✗ 格式化功能一致性测试异常: {e}")
        return False


def test_file_classification_consistency():
    """测试文件分类功能的一致性"""
    print("\n=== 测试文件分类功能一致性 ===")
    
    try:
        from utils.data_parser import classify_file_type, extract_timestamp_from_filename, get_stock_flow_file_format
        
        # 测试文件分类的准确性和一致性
        test_files = [
            # (文件名, 期望类型, 期望时间戳, 期望格式)
            ('09-30_zt_pool.csv', 'limit_up', '093000', None),
            ('fund_flow_rank_20250725_093047.csv', 'stock_flow', '093047', 'rank_format'),
            ('ths_fund_flow.csv', 'stock_flow', None, 'ths_format'),
            ('股票资金流_zssz_20250728_093050.csv', 'stock_flow', '093050', 'exchange_format'),
            ('实时概念资金流_20250728_093453.csv', 'concept', '093453', None),
            ('09-30_ths_big_deal.csv', 'big_deal', '093000', None),
            ('unknown_file.csv', 'other', None, None)
        ]
        
        all_passed = True
        
        print("测试文件分类一致性:")
        for filename, expected_type, expected_timestamp, expected_format in test_files:
            # 测试文件类型分类
            file_type = classify_file_type(filename)
            type_ok = file_type == expected_type
            
            # 测试时间戳提取
            timestamp = extract_timestamp_from_filename(filename)
            timestamp_ok = timestamp == expected_timestamp
            
            # 测试股票资金流格式识别（仅对stock_flow类型）
            if file_type == 'stock_flow' and expected_format:
                format_result = get_stock_flow_file_format(filename)
                format_ok = format_result == expected_format
            else:
                format_ok = True  # 非股票资金流文件不测试格式
            
            overall_ok = type_ok and timestamp_ok and format_ok
            status = "✓" if overall_ok else "✗"
            
            print(f"  {status} {filename}")
            print(f"    类型: {file_type} (期望: {expected_type}) {'✓' if type_ok else '✗'}")
            print(f"    时间: {timestamp} (期望: {expected_timestamp}) {'✓' if timestamp_ok else '✗'}")
            if expected_format:
                format_result = get_stock_flow_file_format(filename)
                print(f"    格式: {format_result} (期望: {expected_format}) {'✓' if format_ok else '✗'}")
            
            if not overall_ok:
                all_passed = False
        
        if all_passed:
            print("\n✅ 所有文件分类功能一致性测试通过")
        else:
            print("\n❌ 部分文件分类功能一致性测试失败")
        
        return all_passed
        
    except Exception as e:
        print(f"✗ 文件分类功能一致性测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("开始功能一致性测试...\n")
    
    results = []
    results.append(test_formatting_consistency())
    results.append(test_file_classification_consistency())
    results.append(test_data_parsing_consistency())
    
    print("\n" + "="*60)
    print("功能一致性测试结果汇总:")
    
    if all(results):
        print("🎉 所有功能一致性测试通过！")
        print("\n✅ 验证结果:")
        print("  - 格式化功能：与原始实现完全一致")
        print("  - 文件分类功能：与原始实现完全一致")
        print("  - 数据解析功能：与原始实现完全一致")
        print("\n🔒 功能保证:")
        print("  - 所有数值转换逻辑保持不变")
        print("  - 所有文件识别规则保持不变")
        print("  - 所有数据处理流程保持不变")
        print("  - 所有输出格式保持不变")
    else:
        print("❌ 部分功能一致性测试失败")
        print("需要检查和修复不一致的功能")
    
    print("="*60)


if __name__ == "__main__":
    main()
